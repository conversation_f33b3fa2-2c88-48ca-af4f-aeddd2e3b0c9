cmake_minimum_required(VERSION 3.10)
project(daemon_logon_ntk CXX)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# CLion build flag configuration (optional feature)
if(NOT DEFINED ENABLE_CLION_BUILD)
    option(ENABLE_CLION_BUILD "Enable CLion-specific build configuration" ON)
endif()

if(ENABLE_CLION_BUILD)
    add_definitions(-DCLION_BUILD)
    message(STATUS "daemon_logon_ntk: CLion build mode enabled")
endif()

# Define directories
set(INC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/inc)
set(LIB_DIR ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
set(BIN_DIR ${CMAKE_CURRENT_SOURCE_DIR}/bin)
set(OBJ_DIR ${CMAKE_CURRENT_SOURCE_DIR}/obj)

# Create directories if they don't exist
file(MAKE_DIRECTORY ${BIN_DIR})
file(MAKE_DIRECTORY ${OBJ_DIR})

# External dependencies
set(EXT_LIB_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../command_logon_ntk/obj")
set(EXT_INC_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../command_logon_ntk/inc")
set(KSLIBRARY_PATH "$ENV{HOME}/library")
set(KSLIBRARY_INC "$ENV{HOME}/library")

# Oracle configuration
set(ORACLE_HOME $ENV{ORACLE_HOME})
if(NOT ORACLE_HOME)
    message(WARNING "ORACLE_HOME environment variable not set")
endif()

set(ORACLE_INCLUDES 
    ${ORACLE_HOME}/rdbms/demo
    ${ORACLE_HOME}/rdbms/public
    ${ORACLE_HOME}/precomp/public
)

set(ORACLE_LIB_DIRS
    ${ORACLE_HOME}/lib
    ${ORACLE_HOME}/plsql/lib
    ${ORACLE_HOME}/network/lib
)

# Compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -lrt -w -DDEBUG=5")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-exceptions -fno-rtti -D_REENTRANT=1")

# Include directories
include_directories(
    ${INC_DIR}
    ${EXT_INC_PATH}
    ${KSLIBRARY_INC}
    ${ORACLE_INCLUDES}
    /usr/include
    /usr/local/openssl/include
)

# Link directories
link_directories(
    ${ORACLE_LIB_DIRS}
    ${KSLIBRARY_PATH}
    /usr/local/openssl/lib
    /lib64
    /lib
)

# External object file
set(EXT_OBJ_FILE "${EXT_LIB_PATH}/sms_ctrlsub++.o")

# Check if external object file exists
if(NOT EXISTS ${EXT_OBJ_FILE})
    message(WARNING "External object file not found: ${EXT_OBJ_FILE}")
    message(WARNING "You may need to build command_logon_ntk first")
endif()

# Common libraries
set(COMMON_LIBS
    nsl
    pthread
    ksbase64
    kssocket
    ksconfig
    ksthread
    crypto
)

# Oracle libraries
set(ORACLE_LIBS
    clntsh
    orapp
    dl
)

# Common library source files
set(LIB_SOURCES
    ${LIB_DIR}/cust_lib_common.c
    ${LIB_DIR}/logonUtil.cpp
    ${LIB_DIR}/adminUtil.cpp
    ${LIB_DIR}/dbUtil.cpp
    ${LIB_DIR}/mmsPacketBase.cpp
    ${LIB_DIR}/mmsPacketSend.cpp
    ${LIB_DIR}/mmsFileProcess.cpp
    ${LIB_DIR}/monitor.cpp
    ${LIB_DIR}/checkCallback.cpp
    ${LIB_DIR}/Encrypt.cpp
    ${LIB_DIR}/packetUtil.cpp
)

# Create a static library for common functionality
add_library(daemon_logon_ntk_common STATIC ${LIB_SOURCES})
target_include_directories(daemon_logon_ntk_common PRIVATE
    ${INC_DIR}
    ${EXT_INC_PATH}
    ${KSLIBRARY_INC}
    ${ORACLE_INCLUDES}
    /usr/include
    /usr/local/openssl/include
)

# Function to add Oracle Pro*C preprocessing
function(add_proc_target target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)
    set(pc_file ${OBJ_DIR}/${source_name}.pc)
    set(cpp_file ${OBJ_DIR}/${source_name}.cpp)
    set(obj_file ${OBJ_DIR}/${source_name}.o)
    
    # Copy source to .pc file
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )
    
    # Run Oracle Pro*C preprocessor
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND proc MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES 
                iname=${pc_file}
                include=${INC_DIR}
                include=${ORACLE_HOME}/precomp/public
                include=${KSLIBRARY_INC}
                include=${EXT_INC_PATH}
                CPP_SUFFIX=cpp CODE=CPP PARSE=NONE SQLCHECK=FULL
                userid=neontk/neontk@NEO223
        COMMAND ${CMAKE_COMMAND} -E remove -f tp*
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${OBJ_DIR}
        COMMENT "Running Pro*C on ${pc_file}"
    )
    
    # Compile the generated C++ file
    add_custom_command(
        OUTPUT ${obj_file}
        COMMAND ${CMAKE_CXX_COMPILER} ${CMAKE_CXX_FLAGS}
                -I${INC_DIR} -I${EXT_INC_PATH} -I${KSLIBRARY_INC}
                -I${ORACLE_HOME}/precomp/public
                -I${ORACLE_HOME}/rdbms/public
                -c ${cpp_file} -o ${obj_file}
        DEPENDS ${cpp_file}
        COMMENT "Compiling ${cpp_file}"
    )
    
    # Create a custom target
    add_custom_target(${target_name} DEPENDS ${obj_file})
    
    # Set a property to store the object file path
    set_target_properties(${target_name} PROPERTIES OBJECT_FILE ${obj_file})
endfunction()

# Oracle Pro*C targets
add_proc_target(DatabaseORA_MMS_proc ${LIB_DIR}/DatabaseORA_MMS.cpp)
add_proc_target(senderNtalkProDB_proc ${SRC_DIR}/senderNtalkProDB.cpp)
#add_proc_target(senderAtalkCryDB_proc ${SRC_DIR}/senderAtalkCryDB.cpp)
add_proc_target(reportMMSProcessDB_proc ${SRC_DIR}/reportMMSProcessDB.cpp)
add_proc_target(senderMMSDB_proc ${SRC_DIR}/senderMMSDB.cpp)
add_proc_target(reportMMSDB_proc ${SRC_DIR}/reportMMSDB.cpp)

# Get object file paths from Pro*C targets
get_target_property(DatabaseORA_MMS_OBJ DatabaseORA_MMS_proc OBJECT_FILE)
get_target_property(senderNtalkProDB_OBJ senderNtalkProDB_proc OBJECT_FILE)
#get_target_property(senderAtalkCryDB_OBJ senderAtalkCryDB_proc OBJECT_FILE)
get_target_property(reportMMSProcessDB_OBJ reportMMSProcessDB_proc OBJECT_FILE)
get_target_property(senderMMSDB_OBJ senderMMSDB_proc OBJECT_FILE)
get_target_property(reportMMSDB_OBJ reportMMSDB_proc OBJECT_FILE)

# Executables
add_executable(logonSession ${SRC_DIR}/logonSession.cpp)
add_executable(logonDB ${SRC_DIR}/logonDB.cpp)
add_executable(admin ${SRC_DIR}/admin.cpp)
add_executable(monitorProcess ${SRC_DIR}/monitorProcess.cpp)
add_executable(adminProcess ${SRC_DIR}/adminProcess.cpp)

# Custom function to create executables with Pro*C objects
function(add_proc_executable target_name main_source proc_objects)
    # Create a custom executable target
    add_executable(${target_name} ${main_source})

    # Add Pro*C object files as sources
    foreach(proc_obj ${proc_objects})
        target_sources(${target_name} PRIVATE ${proc_obj})
    endforeach()

    # Set custom link command to include Pro*C objects
    set_target_properties(${target_name} PROPERTIES
        LINK_DEPENDS "${proc_objects}"
    )
endfunction()

# Create Pro*C executables using custom linking
add_executable(senderNtalkProDB_temp ${SRC_DIR}/dummy.cpp)
add_executable(reportMMSProcDB_temp ${SRC_DIR}/dummy.cpp)

# Create dummy.cpp if it doesn't exist
if(NOT EXISTS ${SRC_DIR}/dummy.cpp)
    file(WRITE ${SRC_DIR}/dummy.cpp "// Dummy file for CMake\nint main() { return 0; }\n")
endif()

# Custom build rules for Pro*C executables
add_custom_target(senderNtalkProDB_build
    COMMAND ${CMAKE_CXX_COMPILER} ${CMAKE_CXX_FLAGS}
            ${senderNtalkProDB_OBJ}
            ${DatabaseORA_MMS_OBJ}
            $<TARGET_OBJECTS:daemon_logon_ntk_common>
            ${EXT_OBJ_FILE}
            -L${KSLIBRARY_PATH}
            -L${ORACLE_HOME}/lib
            -L${ORACLE_HOME}/plsql/lib
            -L${ORACLE_HOME}/network/lib
            -lksbase64 -lkssocket -lksconfig -lksthread -lcrypto
            -lclntsh -lorapp -ldl -lpthread -lnsl
            -o ${BIN_DIR}/senderNtalkProDB
    DEPENDS senderNtalkProDB_proc DatabaseORA_MMS_proc daemon_logon_ntk_common
    COMMENT "Building senderNtalkProDB with Pro*C objects"
)

add_custom_target(reportMMSProcDB_build
    COMMAND ${CMAKE_CXX_COMPILER} ${CMAKE_CXX_FLAGS}
            ${reportMMSProcessDB_OBJ}
            ${DatabaseORA_MMS_OBJ}
            $<TARGET_OBJECTS:daemon_logon_ntk_common>
            ${EXT_OBJ_FILE}
            -L${KSLIBRARY_PATH}
            -L${ORACLE_HOME}/lib
            -L${ORACLE_HOME}/plsql/lib
            -L${ORACLE_HOME}/network/lib
            -lksbase64 -lkssocket -lksconfig -lksthread -lcrypto
            -lclntsh -lorapp -ldl -lpthread -lnsl
            -o ${BIN_DIR}/reportMMSProcDB
    DEPENDS reportMMSProcessDB_proc DatabaseORA_MMS_proc daemon_logon_ntk_common
    COMMENT "Building reportMMSProcDB with Pro*C objects"
)

# Set output names and directories
set_target_properties(logonSession PROPERTIES 
    RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR}
    OUTPUT_NAME logonSession
)

set_target_properties(logonDB PROPERTIES 
    RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR}
    OUTPUT_NAME logonDB
)

set_target_properties(admin PROPERTIES 
    RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR}
    OUTPUT_NAME admin
)

set_target_properties(monitorProcess PROPERTIES 
    RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR}
    OUTPUT_NAME monitorProcess
)

set_target_properties(adminProcess PROPERTIES 
    RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR}
    OUTPUT_NAME adminProcess
)

# Remove temporary executables from ALL target
set_target_properties(senderNtalkProDB_temp PROPERTIES
    EXCLUDE_FROM_ALL TRUE
)

set_target_properties(reportMMSProcDB_temp PROPERTIES
    EXCLUDE_FROM_ALL TRUE
)

# Link libraries for each executable
target_link_libraries(logonSession 
    daemon_logon_ntk_common
    ${COMMON_LIBS}
)

target_link_libraries(logonDB 
    daemon_logon_ntk_common
    ${COMMON_LIBS}
    ${ORACLE_LIBS}
)

target_link_libraries(admin 
    daemon_logon_ntk_common
    ${COMMON_LIBS}
)

target_link_libraries(monitorProcess 
    daemon_logon_ntk_common
    ${COMMON_LIBS}
)

target_link_libraries(adminProcess 
    daemon_logon_ntk_common
    ${COMMON_LIBS}
)

# Note: senderNtalkProDB and reportMMSProcDB are built using custom targets
# due to Oracle Pro*C preprocessing requirements

# Add external object file if it exists
if(EXISTS ${EXT_OBJ_FILE})
    target_sources(logonSession PRIVATE ${EXT_OBJ_FILE})
    target_sources(logonDB PRIVATE ${EXT_OBJ_FILE})
    target_sources(admin PRIVATE ${EXT_OBJ_FILE})
    target_sources(monitorProcess PRIVATE ${EXT_OBJ_FILE})
    target_sources(adminProcess PRIVATE ${EXT_OBJ_FILE})
endif()

# Main build target that includes Pro*C executables
add_custom_target(all_executables
    DEPENDS logonSession logonDB admin monitorProcess adminProcess
            senderNtalkProDB_build reportMMSProcDB_build
    COMMENT "Building all daemon_logon_ntk executables"
)

# Individual build targets for Pro*C executables
add_custom_target(senderNtalkProDB DEPENDS senderNtalkProDB_build)
add_custom_target(reportMMSProcDB DEPENDS reportMMSProcDB_build)

# Custom clean target
add_custom_target(clean-all
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${OBJ_DIR}
    COMMAND ${CMAKE_COMMAND} -E make_directory ${OBJ_DIR}
    COMMAND ${CMAKE_COMMAND} -E remove -f tp*
    COMMENT "Cleaning all generated files"
)

message(STATUS "daemon_logon_ntk CMake configuration completed")
message(STATUS "External object file: ${EXT_OBJ_FILE}")
message(STATUS "Oracle Home: ${ORACLE_HOME}")
message(STATUS "KSkyB Library Path: ${KSLIBRARY_PATH}")
